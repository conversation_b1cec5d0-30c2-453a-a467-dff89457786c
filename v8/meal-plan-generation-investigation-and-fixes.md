# Meal Plan Generation: Investigation and Fix Plan

## TL;DR
- Quick mode runs a single generation pass and (optionally) prefetches up to 3 recipe details.
- Meal Plan mode aggregates requirements per meal type (Breakfast/Lunch/Dinner) and generates one large batch per meal type. After each batch, it also prefetches up to 3 details. This multiplies network calls and time.
- The “hang” you observe is a long busy wait caused by compounded network latency (multiple idea generations + multiple detail prefetches), not a deadlock.
- Low‑risk fix: disable detail prefetch for the structured Meal Plan path. Keep it enabled for Quick only.
- Optional: parallelize per‑meal generation and move heavy non‑UI work off the main actor.

---

## Current Workflow (What the code does)

1) UI triggers generation
- File: `Features/RecipeGenerator/RecipeGeneratorView.swift`
- <PERSON><PERSON> calls `viewModel.generateRecipeIdeas(...)`, which delegates to `generateRecipes(...)`.

2) ViewModel branches by mode
- File: `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- Quick mode:
  - Builds request via `RecipeRequestBuilder.build(...)`
  - Calls `RecipeServiceAdapter.generate(...)` once
  - Shows a preview toast (does not auto‑save)
- Meal Plan (Custom) mode:
  - Builds `MealPlanGenerationRequest` via `RecipeRequestBuilder.buildMealPlanRequest(...)`
  - Calls `recipeService.generateStructuredMealPlan(...)`
  - Saves the resulting plan with overlap policy in `PlanStore.mergeAndSave(...)`
  - Navigates to Plans tab

3) Structured generation per meal type
- File: `Services/StructuredMealPlanGenerator.swift`
- Enumerates concrete (date × meal) slots with same‑day cutoffs
- Aggregates total dishes per meal type across all days
- For each meal type, builds a single “large batch” request and awaits `adapter.generate(...)`
- Assigns the generated recipes into slots in order

4) Adapter behavior (main source of time)
- File: `Services/RecipeServiceAdapter.swift`
- For each requested batch (one call per meal type):
  - Tries `recipeService.generateMealIdeas(...)` (first attempt)
  - If short, tries a second time and de‑duplicates
  - If still short, fills with fallbacks
  - Then prefetches up to 3 details via `GeminiAPIService.generateRecipeDetail(...)`

5) Quick vs Meal Plan difference
- Quick: 1× idea generation (with one retry) + up to 3× detail prefetch
- Meal Plan: (number of meal types) × [idea generation (with retry) + up to 3× detail prefetch]
  - Example: Breakfast+Lunch+Dinner → 3× the work compared to Quick.

---

## Why it appears to “hang”
- Not a deadlock; it’s extended time spent awaiting multiple network calls.
- Each meal type performs: up to 2 idea generations + up to 3 detail prefetches.
- With 2–3 meal types and several days, total calls accumulate and the UI stays in `.loading` longer.
- If the network is slow or rate‑limited, the loading indicator remains visible and feels like a freeze.

Additional notes (correct-by-design behaviors):
- Same‑day meal cutoffs can reduce slot count for “today”, which is intentional.
- Plan merging and retention happen on the main actor in `PlanStore` and add a small extra cost after generation.

---

## Low‑Risk Primary Fix (Recommended)
Disable recipe detail prefetch for the structured Meal Plan path only, while keeping Quick’s UX benefits.

Proposed change:
- Add a parameter to `RecipeServiceAdapter.generate(...)` to control prefetching: `prefetchDetails: Bool = true`.
- Pass `prefetchDetails: false` when called from `StructuredMealPlanGenerator`.

Sketch:
```swift
// Services/RecipeServiceAdapter.swift
func generate(using request: RecipeGenerationRequest,
              cookingTimeMinutes: Int,
              authService: AuthenticationService,
              prefetchDetails: Bool = true) async throws -> [RecipeUIModel] {
    ... // existing logic
    if prefetchDetails {
        await prefetchDetails(for: allModels, pantryIngredients: pantryIngredients, userPreferences: userPrefs)
    }
    return allModels
}
```

```swift
// Services/StructuredMealPlanGenerator.swift
let uiModels = try await adapter.generate(
    using: genReq,
    cookingTimeMinutes: cfg.cookingTimeMinutes,
    authService: authService,
    prefetchDetails: false // disable prefetch for Meal Plan
)
```

Impact:
- Significantly reduces network calls in Meal Plan mode (removes up to 3 detail requests per meal type).
- Preserves Quick’s preview snappiness.
- No changes to business logic or outputs used in the plan.

---

## Optional Enhancements (Nice to have)

1) Parallelize per‑meal generation
- In `StructuredMealPlanGenerator.generatePlan`, use a `TaskGroup` to generate batches for Breakfast/Lunch/Dinner concurrently.
- This reduces wall‑clock time when multiple meal types are selected.

2) Reduce main‑actor blocking
- `RecipeGeneratorViewModel.generateRecipes` currently creates a Task and awaits its value on the main actor.
- Keep heavy work off the main actor, and only marshal UI updates back to the main actor, to keep UI more responsive.

3) Move heavier PlanStore work off main actor
- `PlanStore` is `@MainActor`; consider doing JSON encoding/merging on a background task, then return to the main actor for final state updates and writes.

4) (Future) Single “super batch” across all meal types
- Today it’s “one large batch per meal type”. If desired, extend `RequestDetails` to express a multi‑meal combined batch and generate once.
- This requires broader refactoring and careful distribution logic; lower priority given the recommended fix above.

---

## Validation Plan
- Instrument timings (start/end) around:
  - ViewModel `performGeneration`
  - Adapter `generate` per meal type (with a flag for prefetch enabled/disabled)
  - `prefetchDetails` (begin/end)
- Compare measurements for:
  - Quick (control)
  - Meal Plan BEFORE fix
  - Meal Plan AFTER fix (prefetch disabled)
- Functional checks:
  - Small config (1 day, 1 meal type, 1 dish) vs large config
  - Verify same‑day cutoff skips expected meals
  - Verify plan saved and merged correctly; retention keeps last 4 weeks

Success criteria:
- Meal Plan generation time reduced significantly and predictably
- No regressions in saved plan content or navigation behavior

---

## Risk Assessment
- Disabling prefetch in Meal Plan only affects speculative caching, not the generated plan or UI flow.
- Worst case: opening a specific recipe detail may fetch later (on‑demand) rather than being pre‑cached. Acceptable tradeoff for performance.

---

## Key Code References
- View/UI trigger: `Features/RecipeGenerator/RecipeGeneratorView.swift`
- ViewModel control flow: `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- Plan request building: `Services/RecipeRequestBuilder.swift`
- Structured generator: `Services/StructuredMealPlanGenerator.swift`
- Adapter (ideas + prefetch): `Services/RecipeServiceAdapter.swift`
- Meal cutoffs: `Utils/MealCutoffManager.swift`
- Plan storage/merge: `Services/PlanStore.swift`

---

## Next Steps
- Implement the low‑risk fix (disable prefetch for Meal Plan) and land a small unit test ensuring structured flow does not trigger detail prefetch.
- Optionally, parallelize per‑meal generation and add timing logs to confirm improved wall‑clock time.


---

## Prioritized Action Items (focused on “no-freeze” generation)

1) Disable detail prefetch in Meal Plan path (low-risk, highest impact)
- Add `prefetchDetails: Bool = true` to `RecipeServiceAdapter.generate(...)` and call with `false` from `StructuredMealPlanGenerator`.
- Keeps Quick’s snappy preview while cutting redundant network calls in Meal Plan.

2) Parallelize per‑meal generation (major wall‑clock reduction)
- In `StructuredMealPlanGenerator.generatePlan`, use `withTaskGroup` to run each meal type’s `adapter.generate(...)` concurrently and then merge results.

3) Avoid awaiting the generation task on MainActor (reduce UI “freeze” risk)
- In `RecipeGeneratorViewModel.generateRecipes`, start the background `Task` and do not `await task.value` on the main thread; only update `viewState` via MainActor when states change.

4) Offload PlanStore merge/encode off MainActor (smooth post‑generation save)
- Perform merge + JSON encoding on a background task/actor, then `await MainActor.run { ... }` for final write and UI notifications.

5) Ensure fast cancel responsiveness
- Keep `Task.isCancelled` checks before/after network calls in generation steps so the Cancel button takes effect promptly.

