# V8 PRD — Meal Plan Generation Performance & Stability

## 1. Background
Meal Plan (Custom) generation can appear to “hang” because it performs multiple sequential network calls per meal type and prefetches details after each batch. Quick mode does not suffer from this because it does a single batch with a small prefetch. The investigation document (v8/meal-plan-generation-investigation-and-fixes.md) identifies excessive prefetch in Meal Plan and lack of parallelism as key causes.

## 2. Goals (What success looks like)
- Eliminate perceived “freeze” during Meal Plan generation.
- Reduce total AI calls for typical scenarios (e.g., Lunch+Dinner, 5 days) from up to 10 → 4.
- Keep the main thread responsive throughout generation; Cancel reacts promptly.
- Preserve generated plan content, slot assignment, and retention behavior.

## 3. Non‑Goals
- No change to Quick mode behavior (still prefetches details).
- No major redesign of plan data structures or UI navigation.
- No immediate change to recipe detail UI (details will load on demand for Meal Plan).

## 4. Users & Key Scenarios
- Primary user: home cook planning multiple days (2–3 meal types) with family size and time constraints.
- Core scenario for validation: 5 days; Lunch 3 dishes@40 min; Dinner 4 dishes@60 min; family size 5.

## 5. Scope
- In scope: generation pipeline behavior, concurrency adjustments, main-thread usage, persistence performance.
- Out of scope: new UI pages, new data models, server/API changes beyond concurrency pattern and prefetch flag.

## 6. Assumptions & Dependencies
- Downstream AI service can handle limited parallelism (cap if needed).
- User profile and pantry are available; same-day cutoff logic remains unchanged (Utils/MealCutoffManager.swift).
- PlanStore uses UserDefaults JSON; atomicity remains required.

## 7. Functional Requirements

### FR‑P0: Disable detail prefetch in Meal Plan path
- Add a parameter to `RecipeServiceAdapter.generate(…, prefetchDetails: Bool = true)`.
- In `StructuredMealPlanGenerator`, call `adapter.generate(…, prefetchDetails: false)`.
- Quick mode continues to call with default `true`.
- Acceptance:
  - Meal Plan generation performs 0 detail prefetch calls per meal type.
  - Generated plan content (counts, assignment, dates) is unchanged.

### FR‑P1: Parallelize per‑meal generation with a cap
- In `StructuredMealPlanGenerator.generatePlan`, run a task per selected meal type to call `adapter.generate` concurrently.
- Provide a small concurrency cap (2–3) if external rate limits are present; otherwise concurrent for selected meals.
- Errors in one meal type do not fail the whole plan (graceful empty for that meal type).
- Acceptance:
  - Lunch and Dinner batches run in parallel; total wall‑clock ≈ max(single meal batch times), not sum.
  - No data races; deterministic assignment to slots remains.

### FR‑P1: Keep MainActor responsive
- In `RecipeGeneratorViewModel.generateRecipes`, do not await the full generation task on MainActor.
- Only update `viewState` on MainActor; heavy work runs off MainActor.
- Acceptance: UI remains scrollable and Cancel responds during generation.

### FR‑P2: Offload PlanStore heavy work off MainActor
- Move merge and JSON encoding to a background task/actor; perform final write and UI notifications on MainActor.
- Acceptance: no observable main thread hitch when saving large plans.

### FR‑P2 (Optional): Prefetcher decoupling
- Extract prefetch logic into a `RecipeDetailPrefetcher` service for future tuning.
- No functional change in this release; groundwork for later.

## 8. Non‑Functional Requirements (NFR)
- Performance: Reduce perceived wait by 50%+ in the core scenario; AI calls drop to 4.
- Reliability: No crashes; partial failures per meal type are tolerated and surfaced gracefully.
- Concurrency safety: No data races; respect API rate limits with a small cap.
- Responsiveness: Main thread never blocks on long awaits.

## 9. UX & Copy (Minimal)
- Loading state remains; add “Cancel” affordance (existing) and ensure it is responsive.
- Optional (nice‑to‑have, not required): surface step hints like “Generating Lunch… / Dinner…” when parallelization is enabled.

## 10. Telemetry & Instrumentation
- Log timings:
  - ViewModel: enter/exit performGeneration; state transitions.
  - Adapter: enter/exit generate; tag prefetch enabled/disabled.
  - Prefetch: enter/exit (to measure old vs new cost).
- Metrics to capture per run: number of selected meals, total requested dishes, total AI calls, wall‑clock time.

## 11. Acceptance Criteria (Measurable)
- Core scenario (5 days, Lunch 3@40, Dinner 4@60):
  - BEFORE (baseline): up to 10 AI calls (2 idea gens + up to 3 prefetch per meal × 2 meals), serial.
  - AFTER (P0): exactly 4 AI calls (2 idea gens per meal × 2 meals), no prefetch.
  - AFTER (P0+P1): 4 AI calls and Lunch/Dinner run concurrently.
- UI: Cancel interrupts mid‑generation within 500 ms.
- Plan saved: overlap policy and 4‑week retention remain intact.
- No functional regressions in Quick mode.

## 12. Rollout & Config
- Guard with minimal risk:
  - `prefetchDetails` parameter default true; Structured path sets false explicitly.
  - Concurrency cap configurable via constant/remote flag if available.
- Staged rollout (if desired): enable parallelization behind a feature flag for internal testing first.

## 13. Risks & Mitigations
- Risk: Parallel calls hit rate limit.
  - Mitigation: cap concurrency (e.g., 2); stagger task starts by 200–300 ms if needed.
- Risk: Hidden dependency on prefetch for Meal Plan.
  - Mitigation: confirm detail views load on demand; add smoke test.
- Risk: MainActor offload could introduce save ordering issues.
  - Mitigation: keep final write atomic on MainActor; unit test merge correctness.

## 14. Open Questions
- Do we want progress granularity in UI (per‑meal progress) for Meal Plan?
- Should concurrency cap be configurable remotely (RemoteConfigurationManager)?
- Any A/B measurement windows for timing telemetry?

## 15. Milestones
- P0 (Day 1–2):
  - Add `prefetchDetails` flag; set false for Structured path
  - Unit test: ensure Meal Plan doesn’t prefetch; Quick still does
- P1 (Day 3–5):
  - Parallelize per‑meal generation with cap; smoke test no races, verify counts/assignment
  - ViewModel: stop awaiting full task on MainActor; verify Cancel responsiveness
- P2 (Day 6–7):
  - Offload PlanStore merge/encode; regression tests for overlap/retention
  - Optional: extract `RecipeDetailPrefetcher`

## 16. References
- Investigation & fixes: v8/meal-plan-generation-investigation-and-fixes.md
- Technical implementation guide: v8/README.md
- Key code paths:
  - Features/RecipeGenerator/RecipeGeneratorView.swift
  - Features/RecipeGenerator/RecipeGeneratorViewModel.swift
  - Services/StructuredMealPlanGenerator.swift
  - Services/RecipeServiceAdapter.swift
  - Services/RecipeGenerationService.swift
  - Services/PlanStore.swift
  - Utils/MealCutoffManager.swift

