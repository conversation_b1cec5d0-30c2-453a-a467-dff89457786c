# V9 Do Not Eat Consolidation Implementation Guide

## Overview
This guide describes how to implement the unified **Do Not Eat** experience defined in `v9/do-not-eat-unified-exclusions-prd.md`. The objective is to merge the existing Allergies and Strict Exclusions profile flows into a single chip-based view while keeping all storage, generation, and filtering semantics unchanged.

## File Structure
```
v9/
├── README.md                          # Implementation guide (this document)
└── do-not-eat-unified-exclusions-prd.md # Product requirements document for V9 consolidation
```

## Implementation Approach
Follow the PRD milestones; each phase is self-contained and should be validated before moving on.

1. **Layout Prototype (Day 0–1)**
   - Create `Features/Profile/DoNotEatView.swift` reusing patterns from `AllergiesIntolerancesView.swift` and `StrictExclusionsView.swift`.
   - Implement mock data to validate adaptive chip layout, grouped sections, and the "My Exclusions" summary.

2. **Data Wiring (Day 2)**
   - Hook the view into real `UserPreferences` via `AuthenticationService`.
   - Centralize selection state (allergies, strict exclusions, custom strings) in a lightweight view model/struct to avoid duplicated toggle logic.
   - Ensure case-insensitive dedupe while preserving display casing.

3. **Navigation Integration (Day 3)**
   - Replace the two legacy rows in `Features/Profile/PreferencesEditView.swift` with a single "Do Not Eat" entry.
   - Keep `AllergiesIntolerancesView` and `StrictExclusionsView` compiled but remove them from primary navigation (gated behind `#if DEBUG` if needed).

4. **Polish & Validation (Day 3–4)**
   - Add help sheet, custom item entry, accessibility labels, analytics hooks (optional), and finalize copy.
   - Run regression tests for profile persistence and smoke-test recipe generation to ensure exclusion behaviour remains unchanged.

## Key Code Touchpoints (Scan Results)
- **Preferences routing** – `Features/Profile/PreferencesEditView.swift` currently pushes three dedicated editors (`StrictExclusionsEditView`, `AllergiesIntolerancesEditView`). Replace with new navigation target while preserving save callbacks.
- **Legacy selection logic** – Toggle/save patterns in `Features/Profile/AllergiesIntolerancesView.swift` and `Features/Profile/StrictExclusionsView.swift` (header sections, `toggleAllergy`, `toggleExclusion`, custom entry hints) should inform shared helpers in the new view.
- **Data model** – `Models/UserPreferences.swift` defines separate arrays (`allergiesIntolerances`, `strictExclusions`) and computed counts. No schema change is permitted.
- **Generation safeguards** – `Services/RecipeGenerationService.swift:92` merges both arrays for filtering; verify behaviour through manual smoke tests only.
- **Caching / sync** – `Utils/CacheKeyGenerator.swift` and SwiftData bridging (`Models/SwiftDataModels.swift`) expect unmodified keys. Avoid renaming or refactoring these structures.

## UX & Interaction Guidelines
- Title: **Do Not Eat**; subtitle in muted text: “Items in this list will be strictly excluded from recipe ideas. Mark allergies to avoid any risk.”
- Sections:
  - `My Exclusions` – chips summarising selections with inline remove button (`X`) or toggle.
  - `Allergies & Intolerances` – chips from `AllergyIntolerance.allCases`; highlight medical severity (icon + small “Allergy” tag).
  - `Strict Exclusions` – chips from `StrictExclusion.allCases`; optional sub-grouping (e.g., "Common", "Protein", "Produce").
- Chip styling: rounded rectangle (20pt radius), adaptive width (`LazyVGrid` or custom flow), selected state uses accent fill + white text, unselected uses tertiary outline.
- Custom item entry: top-aligned `+ Add custom item` button launching input sheet. Persisted items display with “Custom” badge.
- Provide trailing `Save` button in navigation bar; rely on implicit back navigation for cancel.

## Data Handling Requirements
- Maintain three internal sets:
  - `selectedAllergies: Set<AllergyIntolerance>`
  - `selectedStrict: Set<StrictExclusion>`
  - `customExclusions: Set<String>` (normalized lowercased key + original text)
- Save path:
  - Allergies → `preferences.allergiesIntolerances`
  - Strict (enum + custom) → `preferences.strictExclusions` (custom items appended as raw strings)
- Deduplication: compare lowercased values across all sets; if user adds a custom item that matches an enum option, prefer the enum entry (remove duplicate string).
- Update `preferences.lastUpdated` and propagate through `authService.updatePreferences` to refresh in-app cache.

## Accessibility & Localization
- VoiceOver: announce chip role (“Allergy, selected, Dairy”).
- Minimum touch target 44x44.
- Strings should be added to the localization pipeline once copy is final (placeholder English acceptable for first pass).

## Telemetry (Optional, per PRD §10)
- Instrument analytics event `do_not_eat_view` with counts for allergies/strict/custom.
- Track custom item additions/removals to inform future UX tweaks.

## Testing & Validation Checklist
- ✅ Unit: helper that splits/normalizes selections.
- ✅ Integration: saving from `DoNotEatView` updates `UserPreferences` and persists across relaunch (check via `AuthenticationService` mock).
- ✅ UI: snapshot or SwiftUI preview verification for chip layout across iPhone sizes.
- ✅ Regression: `FoodPreferencesRegressionTests` and `CrossDeviceSyncTests` must remain green.
- ✅ Manual: generate recipes (Quick + Meal Plan) to confirm no excluded items appear.

## Risks & Mitigations
- **Duplicate entries** – Normalize input before save; add unit tests.
- **User confusion over severity** – Maintain clear section headers and help sheet copy.
- **Layout crowding on small screens** – Prefer adaptive grids with dynamic wrapping; test in compact width.

## Deliverables
- `Features/Profile/DoNotEatView.swift` plus any helper extensions.
- Updated navigation wiring in `PreferencesEditView` (single entry).
- PRD-aligned copy additions and optional analytics hooks.
- README/PRD updates committed under `v9/`.

Follow this guide alongside the PRD to ensure the implementation remains focused, low-risk, and backwards compatible.
