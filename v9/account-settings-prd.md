# Account Settings 重构 PRD

## 背景
- 当前登录用户在 Profile → Preferences 页面最底部看到 “Signed in as”/邮箱/“Sign Out” 区块（参见 `Features/Profile/PreferencesEditView.swift` 中的 `signOutSection`）。
- Settings 页仅包含 Notifications 子页面（`Features/Profile/SettingsView.swift`）。用户无法在一个集中位置管理账号操作或清理本地数据。
- 新需求：把账号相关操作集中到 Settings → Account Settings，并新增 Change Password、Delete Account、Reset Account 功能，Reset Account 需要恢复到初始状态（含清空 pantry、收藏等）。

## 目标
1. 在 Settings 中新增 Account Settings 页面，展示当前登录邮箱并提供账号操作。
2. 从 Profile 主体移除 “Signed in as” 和 Sign Out，将其迁移到 Account Settings。
3. 提供以下操作：Change Password、Reset Account、Delete Account、Sign Out；确保只有适用的操作对用户可用。
4. Reset Account 会清理本地存储的所有用户数据，并把偏好恢复到默认，保持账号仍处于登录状态。
5. 为关键操作增加确认与错误处理，保持现有认证/离线逻辑的一致性。
6. 更新 QA/Analytics 以覆盖新流程。

成功标准：
- 登录用户能在 Settings → Account Settings 中看到邮箱、执行上述操作。
- Profile 页不再露出 Sign Out 相关视图。
- Reset Account 后，pantry、收藏、计划、历史记录等恢复到初始状态；用户无需重新登录。
- Change Password/Delete Account 处理不同身份提供方（Email vs Apple/Google/匿名）并提供反馈。

## 当前体验 & 痛点
- `PreferencesEditView.signOutSection` 提供邮箱展示与 Sign Out 按钮，且混在偏好设置与 telemetry 区块之间，信息架构混乱。
- `SettingsView` 为静态 `List`，只有“Notifications”入口，无账号/隐私设置。
- 应用内尚未实现更改密码、清空数据或删除账号的 UI；`AuthenticationService` 仅支持 signOut/发送 reset password 邮件。
- 各类用户数据散落在 SwiftData (`SwiftDataStorageService`)、UserDefaults (`FavoritesStore`, `PlanStore`, `TelemetryService`, 各种开关)、磁盘 JSON (`HistoryStorageService`)，缺少统一清理。

## 范围内需求
### 信息架构
- Preferences 主体：保留原有卡片，但移除 signOutSection，并将“Settings” 入口文案调整为 “Account & App Settings” 以反映实质内容。
- Settings 根视图：改为 `Form/List` 含两个 section：
  - **Account**：入口到 Account Settings。
  - **App Settings**：沿用 Notifications（保持现有 `NotificationsView`）。未来如果要搬迁 telemetry/隐私，可在此 Section 扩展。

### Account Settings 页面
- Header：
  - 图标（与 profile 保持一致 person.circle）。
  - 显示 “Signed in as” + 邮箱。若 `authService.userEmail` 为空但有 displayName，则展示 displayName 并附 “(no email on file)” 次文案。
  - 若为匿名用户（`isAnonymous == true`）提示“Using temporary guest session”。仅提供 Sign In CTA（跳转现有 SignInView）。匿名用户不显示 Change/Delete/Reset。
- 账号操作列表：使用分组按钮或 `Section`。
  1. `Change Password`（仅当 provider 包含 `password`，禁用 Apple/Google。禁用时展示说明）。
  2. `Reset Account`。
  3. `Delete Account`。
  4. `Sign Out`。
- 每个操作进入确认或二级流程：
  - Change Password：Push 到新表单视图，包含当前密码、新密码、确认密码；调用 Firebase `reauthenticate` + `updatePassword`。若用户未设置密码（OAuth 登录），显示只读提示并提供“发送重设密码邮件”按钮。
  - Reset Account：弹出确认对话框，说明将清空 pantry/favorites/meal plans/quick history 等。确认后执行数据清理流程（见技术部分），成功后 toast 或 banner 提示“已重置至初始状态”。
  - Delete Account：二次确认 + 输入 “DELETE” 文本或密码（按 provider）：
    * Email 密码用户：要求输入密码进行 reauth。
    * Apple/Google：跳转系统 reauth（使用 Firebase 的 credential reauth）。
    * 匿名用户：直接允许删除（即丢弃 session 并回到未登录状态）。
    成功后调用 Firebase `currentUser.delete()`，清理本地数据并返回到 Profile Sign-In 视图。
  - Sign Out：沿用现有 confirm alert → `authService.signOut()`，完成后导航返回 Profile。

### Reset Account 清理范围
执行顺序建议集中在新 `AccountResetManager`：
1. 暂停 UI 操作（显示 Loading overlay）。
2. Pantry：调用 `SwiftDataStorageService.clearAllIngredients()`，并通过 `PantryService` 刷新内存状态（重载空列表，清除 recentlyAdded）。
3. Favorites：`FavoritesStore` 新增 `clearAll()`，移除 `favorites.recipes.v1` & `favorites.snapshots.v1`。
4. Meal Plans：`PlanStore` 新增 `clearAll()`，移除 `planstore.lastQuick.v1`、`planstore.lastMealPrep.v1`、`planstore.retentionNotice.v1`。
5. Quick History：`HistoryStorageService` 新增 `clearAllQuick()`，删除 index key (`StorageConfiguration.quickHistoryIndexKey`) 与磁盘文件夹 `QuickHistory/`（可在 `DiskStorageManager` 新增删除文件夹能力）。
6. 生成记录/缓存：
   - `recipes.selectedTab`, `UITEST_*` keys 若存在可保留（仅测试）→ 标记为非关键。
   - Telemetry opt-in (`telemetry_opt_in`)、Food expiration toggle (`foodExpirationReminderEnabled`)、suppress 标记 (`expirationSuppressNoonDate`) 重置为默认（false/移除）。
   - Remote config cache (`remote_config_cache_v1`, `remote_config_timestamp_v1`) 可清空，让用户重新走首次体验。
7. 偏好：
   - 通过 `AuthenticationService.updatePreferences(.createDefault(for: uid))` 保存默认结构并调用 `UserProfileService.savePreferences`（若在线）。
   - 清理本地 SwiftData `SavedUserPreferences`（可沿用 `saveUserPreferences` 写入默认）。
8. 清除本地图像缓存：调用 `DiskStorageManager.cleanupExpired` 无法立即清空，需要新方法 `purgeFolder(name:)`。
9. 完成后 dismiss loading，显示成功提示。
10. 若任何步骤失败，显示错误并停止剩余步骤，让用户重试；必要时回滚（至少记录日志）。

### 验证 & 错误处理
- 网络离线：
  - Reset Account 主要操作本地，不依赖网络。FireStore 同步失败时提示“将稍后同步”。
  - Delete Account/Change Password 需要在线。若 `OptimizedNetworkService` 判定离线，禁用按钮或弹出 offline 提示。
- Re-auth 超时或密码错误，返回可读错误并允许重试。
- Loading/禁用：敏感操作进行中禁用重复点击。

### Telemetry / Analytics
- 如果用户 opt-in：
  - `account_reset_confirmed`
  - `account_delete_confirmed`
  - `account_change_password_success`
  - `account_sign_out`
  参数包含 provider、耗时、结果(success/failure)。

### 无障碍 & 设计细节
- Account Settings 使用标准 iOS List/Form，支持 Dynamic Type。
- 所有 destructive 操作标红，配合 `role: .destructive`。
- 确认文案双语（英文默认，简体中文本地化在 Base.lproj / zh-Hans.lproj 中补充）。

## 非功能 / Out of Scope
- 不改动 Notifications 逻辑本身。
- 不实现多账号切换。
- 不变更 Sign-In 流程或第三方绑定。
- 不处理服务端批量删除数据（Reset 仅影响本地 + Firestore 偏好）。

## 技术实现要点与依赖
- **视图更新**：
  - 更新 `PreferencesEditView` 去除 `signOutSection` & alert；修改 Settings 卡片文案。
  - 扩展 `SettingsView` 为 Sectioned List；新增 `AccountSettingsView`、`ChangePasswordView` 等 SwiftUI 视图。
- **服务层扩展**：
  - `AuthenticationService` 新增 `changePassword(current:new:)`, `deleteAccount(with:)`, `reauthenticate(email:password:)` 等函数，并处理 provider 区分；失败时暴露 `AuthError`。
  - 新建 `AccountResetManager`（或放在 `ServiceContainer`）聚合清理逻辑；确保线程安全（SwiftData 操作在主线程）。
  - `FavoritesStore`, `PlanStore`, `HistoryStorageService`, `DiskStorageManager` 添加清理 API。
  - `SwiftDataStorageService` 添加 `deleteUserPreferences()`（或复用 `saveUserPreferences(.default)`）。
- **依赖**：Firebase Auth 已在项目中；Change/Delete 操作需要在 Info.plist 中确保 reCAPTCHA/安全设置已配置（需与后端确认）。

## QA 验收清单
1. 邮箱密码用户：
   - 进入 Account Settings 显示正确邮箱。
   - 成功修改密码（包含弱密码错误提示、错误密码 reauth 检查）。
   - Reset Account 后 pantry 列表为空、favorites tab 空、plan 历史清零；Profile 中偏好回复默认。
   - Delete Account 退出到未登录状态，本地数据已清空。
2. Apple/Google 登录：
   - Change Password 禁用并提示使用供应商管理。
   - Reset Account 正常执行。
   - Delete Account 触发系统 reauth（测试凭证）。
3. 匿名用户：Account Settings 仅显示 Sign In CTA。
4. 离线状态下尝试敏感操作，出现合理提示。
5. 回归 Sign Out：确认仍然有效并在 Account Settings 内显示。
6. Telemetry opt-in 情况下捕获事件。

## 时间评估 & 里程碑（建议）
- 设计确认：1d
- 视图实现：2d
- 服务层与数据清理：2-3d
- QA & 回归：1-2d

## 待确认问题
1. Reset Account 是否需要同步删除 Firestore 中的 Quick History/Favorites（如果未来有云同步）。目前仅清理本地 + 偏好。
2. Delete Account 前是否需导出数据提示？
3. Telemetry 事件名称是否符合现有命名规范。
4. 匿名用户是否允许访问 Settings → Account Settings？（建议允许但显示受限 CTA）。

