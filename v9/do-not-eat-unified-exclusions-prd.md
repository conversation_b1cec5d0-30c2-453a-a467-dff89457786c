# V9 PRD — Do Not Eat Consolidated Preferences

## 1. Background
Users currently manage “Allergies & Intolerances” and “Strict Exclusions” through two distinct profile screens. Feedback shows the split creates friction, while reference apps demonstrate a single, chip-based exclusion hub with clearer discoverability. Previous analysis recommends unifying the UI while leaving the underlying data model intact to avoid large migrations.

## 2. Goals
- Provide one consolidated "Do Not Eat" entry in Profile settings.
- Offer a modern, chip-driven selection experience grouped by exclusion type.
- Preserve existing storage semantics and generator behaviour (prompts, filtering).
- Enable custom exclusions entry without regressions.

## 3. Non-Goals
- No schema changes for `UserPreferences`, Firestore, or SwiftData.
- No changes to recipe generation prompts or filtering logic.
- No introduction of remote configuration or feature flags beyond existing ones.

## 4. Users & Scenarios
- Primary: users with food allergies or strong dislikes configuring exclusions once and expecting consistent filtering.
- Scenario: user visits Profile → Do Not Eat, reviews current exclusions, adds a custom item (“Kale”), ensures it persists, and confirms recipe generation respects combined preferences.

## 5. Scope
- In scope: Profile UI updates, unified selection logic, persistence hookup, basic analytics.
- Out of scope: redesign of dietary restrictions, backend API adjustments, copy localization changes beyond new strings.

## 6. Dependencies & Assumptions
- Authentication service continues providing `UserPreferences` with populated arrays.
- Existing save pipeline (`userProfileService.savePreferencesOfflineAware`) remains available and reliable.
- Chip styling leverages current SwiftUI components without introducing third-party dependencies.

## 7. Functional Requirements

### FR-01: Single Entry Point
- Replace “Allergies & Intolerances” and “Strict Exclusions” rows with one "Do Not Eat" row in `PreferencesEditView`.
- Row displays aggregated count of exclusions (sum of allergies + strict + custom).
- Legacy views remain reachable via debug-only navigation (if required).

### FR-02: Consolidated Selection Screen
- Create `DoNotEatView` with navigation title “Do Not Eat”.
- Display current selections in a "My Exclusions" section (chips with remove affordance).
- Group available options into sections:
  - Allergies & Intolerances (source: `AllergyIntolerance.allCases`).
  - Strict Exclusions (source: `StrictExclusion.allCases`, optionally sub-grouped by category for layout clarity).
- Chips toggle selection on tap; selected state uses accent fill, unselected uses tertiary outline.
- Selected chips show contextual tags (e.g., “Allergy”, “Strict”, “Custom”).

### FR-03: Custom Item Support
- Provide prominent “Add custom item” button near top “My Exclusions” section (reuse existing add sheet flow).
- Custom entry enforces non-empty text, trims whitespace, deduplicates case-insensitively against all existing selections.
- Saved custom values append to `preferences.strictExclusions` (string-based) while marked as `Custom` in UI.

### FR-04: Persistence & Validation
- On save, split selections:
  - Items matching `AllergyIntolerance` enum → `preferences.allergiesIntolerances`.
  - Items matching `StrictExclusion` enum or custom strings → `preferences.strictExclusions` (preserve raw values for customs).
- Ensure duplicates removed using case-insensitive comparison while retaining canonical casing for display (enum rawValue or user input).
- Continue using `userProfileService.savePreferencesOfflineAware` for persistence and `authService.updatePreferences` for local cache.
- Success toast and error handling mimic current flows.

### FR-05: Help & Education
- Provide question-mark button near “My Exclusions” calling `Help` sheet explaining strict avoidance and difference between allergies vs dislikes.
- Optional grey subtext under title: “Items in this list will be strictly excluded from recipe ideas. Mark allergies to avoid any risk.”

### FR-06: Accessibility & Responsiveness
- Support VoiceOver by announcing chip state, category, and selection instructions.
- Layout adapts to compact width (adaptive grid or flow layout). Minimum hit target 44x44.

## 8. Non-Functional Requirements
- Performance: Screen loads available exclusions within 150 ms on iPhone 12 equivalent.
- Reliability: Saving preferences must succeed offline with queueing (existing behaviour).
- Usability: QA validation ensures zero orphaned selections; custom items appear instantly in top section.

## 9. UX Specifications
- Layout mimics reference app with grouped chips and floating sections.
- Colors follow existing design tokens (`.orange` accent for allergies, `.blue` accent for strict by default or unified accent to reduce complexity).
- Chips: rounded rectangle with 20pt corner radius, padding (horizontal 12, vertical 8).
- Buttons: `+ Add custom item`, `Save` on nav bar trailing, `Cancel` available via back swipe/chevron.
- Empty state copy for “My Exclusions”: “No items selected yet.”

## 10. Telemetry & Instrumentation (Optional V2)
- Log screen view event with counts of allergies/strict/custom.
- Log custom addition and removal events for future UX improvement.

## 11. Acceptance Criteria
- Navigating to Profile shows only one “Do Not Eat” row with accurate count.
- Selecting/deselecting chips updates “My Exclusions” and counts immediately.
- Adding a custom item adds a chip labelled `Custom`, persists after relaunch, and appears in generator prompt via strict exclusions.
- Existing regressions tests (`FoodPreferencesRegressionTests`) pass without modification.
- Quick and Meal Plan generations continue to omit combined exclusions (manual smoke test).

## 12. Rollout Plan
- Ship behind no new flag (UI only). Optionally guard navigation entry with remote flag for phased release.
- Monitor crash logs and user feedback for 1 week post-release.

## 13. Risks & Mitigations
- **Risk:** Misclassification of custom values leading to duplicates.
  - **Mitigation:** Normalize strings for comparison; unit test helper.
- **Risk:** Users misinterpret severity differences.
  - **Mitigation:** Provide help text emphasising medical implications.
- **Risk:** Layout crowding on small screens.
  - **Mitigation:** Use adaptive grid with wrap and scroll.

## 14. Milestones
- Day 0–1: Implement `DoNotEatView` layout with mock data.
- Day 2: Connect real data, selection helpers, custom entry.
- Day 3: Integrate routing, polish copy, add accessibility/QA.
- Day 4: Regression testing, telemetry hooks (if chosen), prepare release notes.

## 15. Appendix
- Legacy views kept under `#if DEBUG` for potential fallback.
- Reference screens: see shared screenshot from competitor app.
